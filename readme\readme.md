# PAAS-Console 技术分析报告

## 项目概述

PAAS-Console 是一个面向技术人员的数据查询/刷库工具，基于 Spring MVC + MyBatis 架构构建的企业级 Java Web 应用。该项目主要为技术运维人员提供跨数据库的数据查询、元数据管理、数据迁移、系统监控等功能。

## 对外提供的核心功能

根据实际部署的前台功能菜单和Controller实现，系统提供以下核心功能模块：

### 1. 元数据管理
**实现方式**: 通过多个Controller提供不同维度的元数据操作
- **数据查询** (`DataQueryController`):
  - 支持按场景查询数据(`findDataByScene`)，可选择数据库(db)或ES搜索引擎(es)作为数据源
  - 元数据字段查询(`findMtField`)，获取对象的字段信息
- **Schema管理** (`SchemaQueryController`):
  - 支持PostgreSQL的Schema隔离查询，区分新旧Schema类型
  - 提供多租户、多模块(CRM/BI)的Schema路由查询
- **对象描述查询** (`DescribeQueryController`):
  - 查询元数据对象的API名称和显示名称映射
  - 提供字段、布局、数据服务的统一查询接口
- **数据字典导出** (`MetadataAttachmentController`):
  - 支持按对象API名称导出数据字典
  - 提供Excel格式的元数据导出功能
- **SQL执行工具** (`MetadataUtilsController`):
  - 支持PostgreSQL和ClickHouse的SQL生成和执行
  - 通过HTTP调用远程元数据服务获取SQL

### 2. DTS数据传输服务
**实现方式**: 通过`DtsService`提供企业间数据传输能力
- **数据库连接检查** (`checkJdbcUrl`): 验证目标数据库连接的有效性
- **数据一致性验证** (`checkDtsData`): 对比源库和目标库的数据一致性
- **配置生成** (`generateDtsConfig`): 生成DTS数据传输配置
- **Topic管理** (`getDtsTopicByEi`): 获取企业对应的DTS Topic信息
- **SQL执行代理**: 支持查询SQL和更新SQL的远程执行，通过Agent代理访问目标数据库

### 3. 工作流管理
**实现方式**: 基于MongoDB存储的工作流引擎
- **流程定义查询**: 查询工作流定义信息，支持按流程类型、启用状态、应用ID筛选
- **流程实例查询**: 查询工作流实例的执行状态和历史记录
- **任务查询**: 查询工作流任务的详细信息和处理状态
- **ObjectId查询**: 通过ObjectId查询相关的工作流数据和元数据信息

### 4. 多语言支持(i18n)
**实现方式**: 通过`I18nController`提供国际化数据迁移
- **词条迁移** (`migrateStart`):
  - 同步中文词条到国际化平台
  - 迁移预设对象的name、字段的label、关联字段的target_related_list_label
  - 支持批量企业的国际化数据迁移

### 5. 数据权限管理
**实现方式**: 基于复杂的权限计算引擎和Redis缓存
- **权限查询** (`DataRightsQueryController`):
  - 获取指定对象的数据权限信息(`queryDataRights`)
  - 新数据权限检查(`checkDataRights`)，验证用户对特定数据的访问权限
- **权限计算** (`DataRightsController`):
  - 支持多维度权限计算：个人权限、部门权限、负责人权限、外部权限等
  - 提供权限图谱可视化，展示权限关系网络
- **缓存管理**:
  - 数据权限缓存的初始化和刷新
  - 支持用户圈子、部门缓存、负责人缓存的管理

### 6. 沙箱管理
**实现方式**: 通过`SandBoxController`提供事务查询能力
- **事务查询** (`queryByTransactionId`):
  - 通过事务ID查询沙箱环境中的事务状态
  - 调用远程沙箱服务获取事务详细信息

### 7. 功能权限管理
**实现方式**: 通过`FuncController`提供角色权限查询
- **租户角色查询** (`tenantRoleQuery`): 查询租户级别的角色配置
- **用户角色查询** (`userRoleQuery`): 查询用户的角色分配情况
- **角色代码获取** (`getRoleCodes`): 获取租户下的所有角色代码列表

### 8. 全局搜索
**实现方式**: 通过`SearchController`和`HubblePageController`提供搜索能力
- **搜索页面** (`SearchController`): 提供统一的搜索入口页面
- **Hubble搜索** (`HubblePageController`):
  - 支持跨对象的关键词搜索
  - 可指定搜索的API名称范围和返回结果数量

### 9. 分版查询
**实现方式**: 目前主要通过元数据查询实现版本对比功能
- 通过元数据查询接口获取不同版本的数据结构
- 支持Schema版本的对比和查询

### 10. 组织架构管理
**实现方式**: 通过Dubbo服务调用外部组织架构系统
- **企业信息查询**: 调用`fs-uc-api`获取企业基本信息
- **部门管理**: 获取部门树状结构和部门详细信息
- **员工管理**: 查询员工信息和组织关系

### 11. 审计日志
**实现方式**: 基于AOP切面自动记录和Redis缓存
- **操作日志记录**: 通过`@SystemControllerLog`注解自动记录用户操作
- **日志查询**: 提供审计日志的查询和分析功能
- **日志清理**: 通过`DelAuditLogService`定时清理过期日志

### 12. HAMSTER数据迁移
**实现方式**: 基于模板化的数据迁移引擎
- **任务管理** (`HamsterTaskController`):
  - 创建、编辑、删除迁移任务
  - 支持Schema到Schema的迁移模板
- **迁移执行**:
  - 单任务迁移和批量迁移
  - 迁移状态监控和日志记录
- **权限控制**: 基于Shiro的权限验证，区分管理员和编辑权限

### 13. 基础平台
**实现方式**: 提供系统级的基础服务
- **计算服务** (`calculateController`):
  - 触发字段计算和更新(`triggerCalculateAndUpdate`)
  - 支持指定对象和字段的批量计算
- **调度任务管理** (`schedulerTaskController`):
  - 批量取消调度任务
  - 任务状态管理和监控
- **重复数据刷新** (`DuplicatedRefreshController`):
  - 支持不同类型的数据刷新
  - 批量企业数据刷新和失败处理

## 第三方数据源操作方式

### 1. 多租户数据库路由
- **路由策略**: 通过`DbRouterClient`实现动态数据源路由
- **租户隔离**: 基于Schema的租户数据隔离(`ConsoleTenantPolicy`)
- **连接管理**: 使用HikariCP连接池管理多数据源连接

### 2. 数据库访问实现
```groovy
// 多租户数据源路由示例
RouterInfo routerInfo = dbRouterClient.queryRouterInfo(
    tenantId, "CRM", "paas-console", DialectUtil.POSTGRESQL, usePgBouncer
)
// 动态切换数据源
TenantContext.builder()
    .username(routerInfo.userName)
    .password(routerInfo.passWord)
    .url(routerInfo.jdbcUrl)
    .schema(routerInfo.standalone ? "sch_" + tenantId : "public")
    .build()
```

### 3. 外部服务集成
- **Dubbo服务调用**: 通过Dubbo调用企业信息服务(`fs-uc-api`)
- **HTTP API调用**: 使用OkHttp客户端调用外部REST API
- **MongoDB操作**: 工作流数据的MongoDB存储和查询
- **Redis缓存**: 数据权限和会话信息的缓存操作

## 技术架构与模块分层

### 核心技术栈
- **开发语言**: Java 8+ / Groovy
- **Web框架**: Spring MVC 4.x
- **持久层**: MyBatis 3.x + 动态数据源
- **构建工具**: Maven 3.x
- **模板引擎**: FreeMarker 2.3.23

### 分层架构
```
├── Web层 (Controller) - HTTP请求处理
│   ├── /metadata/* - 元数据管理接口
│   ├── /datarights/* - 数据权限接口
│   ├── /hamster/* - 数据迁移接口
│   ├── /dts/* - 数据传输接口
│   ├── /organization/* - 组织架构接口
│   └── /license/* - 许可证管理接口
├── Service层 - 业务逻辑处理
│   ├── 数据查询服务 (多数据库支持)
│   ├── 数据迁移服务 (Hamster)
│   ├── 外部服务代理 (Dubbo/HTTP)
│   └── 缓存管理服务 (Redis)
├── Mapper层 - 数据访问
│   ├── 动态数据源路由
│   ├── MyBatis Mapper接口
│   └── 多租户SQL拦截器
└── Entity/Bean层 - 数据模型
    ├── 数据库实体类
    └── API传输对象(DTO)
```

## 中间件集成与用途

### 1. Redis缓存系统
- **数据权限缓存**: 缓存用户数据权限信息，提高权限验证性能
- **审计日志缓存**: 缓存未读的审计日志消息
- **会话管理**: 用户会话信息的缓存存储
- **配置**: `paas-console-redis`配置项，基于Jedis客户端

### 2. XXL-Job分布式任务调度
- **许可证同步**: `LicenseSyncJobHandler` - 定时同步企业许可证信息
- **日志清理**: `DelAuditLogService` - 定时清理过期的审计日志
- **任务监控**: 提供任务执行状态监控和日志管理
- **配置**: 支持集群部署和故障转移

### 3. MongoDB文档数据库
- **工作流数据**: 存储工作流实例、任务、历史数据
- **复杂查询**: 支持工作流的复杂条件查询和聚合操作
- **配置**: `mongo-support-workflow`配置项
- **连接管理**: 支持连接池和读写分离

### 4. Dubbo微服务调用
- **企业信息服务**: 调用`fs-uc-api`获取企业基本信息
- **组织架构服务**: 获取部门、员工等组织架构数据
- **注册中心**: 使用Zookeeper作为服务注册中心
- **服务治理**: 支持服务监控、负载均衡、故障转移

### 5. ClickHouse大数据分析
- **大数据统计**: 处理海量数据的统计分析查询
- **实时查询**: 支持实时数据查询和报表生成
- **高性能**: 列式存储，查询性能优异
- **版本**: 使用0.3.2-patch11版本的JDBC驱动

### 6. Elasticsearch搜索引擎(间接支持)
- **全文搜索**: 通过模板页面支持ES数据的全文搜索
- **数据分析**: 提供数据分析和可视化能力
- **集成方式**: 通过HTTP API调用ES服务

## 数据库支持与多租户架构

### 支持的数据库类型
- **PostgreSQL**: 主要数据库，支持Schema级别的租户隔离
- **MySQL**: 兼容支持，版本5.1.47，用于传统业务系统
- **SQL Server**: 企业级数据库支持，用于大型企业客户
- **ClickHouse**: 专用于大数据分析和实时查询
- **MongoDB**: 文档数据库，主要用于工作流和非结构化数据

### 多租户数据隔离实现
```groovy
// 租户策略实现
@Component
class ConsoleTenantPolicy implements TenantPolicy {
    @Override
    TenantContext get(String tenantId, boolean readOnly) {
        // 查询租户路由信息
        RouterInfo routerInfo = dbRouterClient.queryRouterInfo(
            tenantId, "CRM", "paas-console", DialectUtil.POSTGRESQL, usePgBouncer
        )

        // 构建租户上下文
        return TenantContext.builder()
            .username(routerInfo.userName)
            .password(routerInfo.passWord)
            .url(routerInfo.jdbcUrl)
            .schema(routerInfo.standalone ? "sch_" + tenantId : "public")
            .build()
    }
}
```

### 动态数据源配置
- **路由客户端**: `DbRouterClient`负责查询租户的数据库路由信息
- **连接池管理**: 使用HikariCP管理多数据源连接池
- **读写分离**: 支持主从数据库的读写分离
- **故障转移**: 自动切换到备用数据源

## 开发与测试

### 环境要求
- JDK 8+
- Maven 3.x
- IDE支持Groovy (推荐IntelliJ IDEA)

### 项目启动
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包部署
mvn clean package
```

### API测试工具
项目提供了完整的HTTP测试文件(位于`/http`目录):

- **CmsController.http**: 配置管理API测试
- **DataCountController.http**: 数据统计API测试
- **bizconfQuery.http**: 业务配置查询测试
- **paas.http**: 通用平台API测试

### 部署方式
- **打包格式**: WAR包，支持Tomcat/Jetty容器
- **配置管理**: 基于配置中心的动态配置
- **监控集成**: JVM监控、应用性能监控
- **日志系统**: Logback日志框架，支持日志轮转

## 核心依赖库

### 外部依赖
- **FastJSON**: JSON数据序列化/反序列化
- **EasyExcel**: Excel文件读写处理
- **Apache HttpClient**: HTTP客户端通信
- **FreeMarker**: Web页面模板引擎

### 内部服务依赖
- **fs-metadata-provider**: 元数据服务提供者
- **fs-paas-org-provider**: 组织架构服务
- **fs-paas-license-api**: 许可证管理API
- **hamster-client**: 数据迁移客户端
- **paas-common**: 公共组件库

## 技术特点

1. **多租户架构**: 完善的多租户数据隔离，支持企业级SaaS部署
2. **多数据库支持**: 统一接口操作PostgreSQL、MySQL、SQL Server、ClickHouse等
3. **高性能**: 连接池管理、缓存机制、读写分离优化查询性能
4. **模块化设计**: 清晰的分层架构，便于功能扩展和维护
5. **丰富的集成**: 支持多种中间件和外部服务集成

## 其他功能特性

### 安全与权限
- 基于角色的访问控制和数据权限管理
- 数据脱敏功能和数据库密码加密存储
- 完整的操作审计日志记录

### 系统监控
- JVM性能监控和应用健康检查
- 基于Logback的日志管理系统
- 支持日志轮转和集中化日志收集

## 相关文档

### 项目结构详细说明
详细的项目包结构和代码组织说明请参考：[项目结构说明文档](structure.md)

该文档包含：
- src/main目录下各个包的详细说明
- 每个包的职责和包含的主要类
- 代码组织的设计原则和模块划分
- HTTP测试文件的使用方法

### 新功能开发指南
新功能开发的完整流程和规范请参考：[开发指南文档](development-guide.md)

该文档包含：
- 七步开发流程详解
- 代码示例和模板
- 开发规范和注意事项
- 测试和部署指导

## 总结

PAAS-Console是一个专业的企业级数据管理和运维工具，主要面向技术人员提供跨数据库的数据查询、迁移、监控等功能。项目采用现代化的技术架构，具有良好的扩展性和维护性，特别在多租户数据隔离、多数据库支持、中间件集成等方面有完善的实现。

系统通过清晰的功能模块划分，为技术人员提供了完整的数据管理解决方案，涵盖了从数据查询、权限管理到数据迁移、系统监控的各个方面。