# PAAS-Console 项目结构说明

## src/main 目录结构详解

### 1. annotation 包 - 注解定义
- **SystemControllerLog.groovy**: 系统控制器日志注解，用于标记需要记录审计日志的Controller方法
- **HamsterControllerLog.java**: Hamster模块专用的控制器日志注解

### 2. aoplog 包 - AOP日志切面
- **SystemLogAspect.groovy**: 系统日志切面，拦截带有@SystemControllerLog注解的方法，自动记录操作审计日志
- **HamsterLogAspect.java**: Hamster模块的日志切面，专门处理数据迁移操作的日志记录

### 3. bean 包 - 数据传输对象(DTO)
按功能模块组织的数据传输对象：

#### calculate/ - 计算相关DTO
- `FindCalculateReasonEntity.groovy`: 计算原因查询实体
- `TriggerCalculate.groovy`: 触发计算的参数对象

#### datarights/ - 数据权限相关DTO
- `DataRightsContext.groovy`: 数据权限上下文
- `InitCacheArg.groovy`: 缓存初始化参数
- `ShareArg.groovy`: 数据共享参数
- `BasicQueryArg.groovy`: 基础查询参数
- `LeaderArg.groovy`: 负责人参数
- `Page.groovy`: 分页参数

#### dts/ - 数据传输服务相关DTO
- `DtsCheckDataArg.java`: DTS数据检查参数
- `DtsConfigArg.java`: DTS配置参数
- `DtsQueryResult.java`: DTS查询结果

#### license/ - 许可证相关DTO
- `OverviewDto.groovy`: 许可证概览DTO
- `ParaInfoDto.groovy`: 参数信息DTO
- `PvInfo.groovy`: 产品版本信息
- `QuoteOverviewRequest.groovy`: 报价概览请求

#### metadata/ - 元数据相关DTO
- `DataResult.groovy`: 数据查询结果
- `SceneType.groovy`: 场景类型枚举
- `SearchSceneArg.groovy`: 搜索场景参数
- `DbResource.groovy`: 数据库资源信息
- `RestoreField.groovy`: 恢复字段信息
- `TenantDataCount.groovy`: 租户数据统计

#### organization/ - 组织架构相关DTO
- `arg/`: 参数对象
- `object/`: 业务对象

#### workflow/ - 工作流相关DTO
- `WorkflowPojo.groovy`: 工作流对象
- `TaskPojo.groovy`: 任务对象
- `ActivityInstancePojo.groovy`: 活动实例对象
- `BatchQueryTaskArg.groovy`: 批量查询任务参数
- `WorkflowInstancePojo.groovy`: 工作流实例对象

### 4. entity 包 - 数据库实体类
按业务领域组织的数据库实体：

#### anonymous/ - 匿名访问相关实体
- `GetStoreTable.groovy`: 获取存储表实体
- `GetStoreTablePojo.groovy`: 存储表POJO

#### datarights/ - 数据权限实体
- `DtAuth.groovy`: 数据权限认证实体
- `Personnel.groovy`: 人员实体
- `Principal.groovy`: 主体实体
- `Department.groovy`: 部门实体
- `DepartmentUser.groovy`: 部门用户关系
- `EntityShareGroup.groovy`: 实体共享组
- `GroupUser.groovy`: 组用户关系

#### license/ - 许可证实体
- `LicenseObjectEntity.groovy`: 许可证对象实体
- `ModuleInfoEntity.groovy`: 模块信息实体
- `ModuleInfoIdCodeEntity.groovy`: 模块信息ID代码实体
- `ProductLicenseEntity.groovy`: 产品许可证实体

#### log/ - 日志相关实体
- `AuditLog.groovy`: 审计日志实体
- `SpecialTable.groovy`: 专表实体
- `DescribeStatistics.groovy`: 描述统计实体

#### metadata/ - 元数据实体
- `MtDescribe.groovy`: 元数据描述实体
- `MtUnique.groovy`: 元数据唯一性实体
- `DictExcelData.java`: 字典Excel数据
- `FileExcelData.groovy`: 文件Excel数据

#### organization/ - 组织架构实体
- `DepartmentEntity.groovy`: 部门实体
- `EmployeeEntity.groovy`: 员工实体

#### workflow/ - 工作流实体
- `TaskEntity.groovy`: 任务实体

### 5. exception 包 - 异常定义
- **workflow/WorkflowAdminException.groovy**: 工作流管理异常，提供详细的错误码和异常信息

### 6. job 包 - 定时任务
- **LicenseSyncJobHandler.groovy**: 许可证同步定时任务，基于XXL-Job框架实现

### 7. license 包 - 许可证模块
- **mapper/**: 许可证相关的MyBatis Mapper接口，独立的数据源配置

### 8. mapper 包 - 数据访问层
按业务模块组织的MyBatis Mapper接口：

#### datarights/ - 数据权限Mapper
- `DataRightsQueryMapper.groovy`: 数据权限查询
- `PersonnelMapper.groovy`: 人员信息查询
- `DataRightsDataMapper.groovy`: 数据权限数据操作

#### func/ - 功能权限Mapper
- 功能权限相关的数据访问接口

#### log/ - 日志Mapper
- 审计日志和系统日志的数据访问

#### metadata/ - 元数据Mapper
- `DataMapper.groovy`: 通用数据查询
- `FieldMapper.groovy`: 字段信息查询
- `MtDataMapper.groovy`: 元数据操作
- `StatMapper.groovy`: 统计查询

#### organization/ - 组织架构Mapper
- 部门、员工等组织架构数据访问

### 9. service 包 - 业务逻辑层
按功能模块组织的服务类：

#### 根目录核心服务
- `ConsoleTenantPolicy.groovy`: 多租户策略实现
- `DataMaskingService.groovy`: 数据脱敏服务
- `OKHttpService.groovy`: HTTP客户端服务
- `OrgDubboService.groovy`: 组织架构Dubbo服务
- `PgConnectionService.groovy`: PostgreSQL连接服务
- `BizconfService.groovy`: 业务配置服务

#### basicPlatform/ - 基础平台服务
- 基础平台相关的业务逻辑

#### datarights/ - 数据权限服务
- 数据权限计算、查询、缓存管理

#### dts/ - 数据传输服务
- 数据库同步、迁移、SQL执行服务

#### functional/ - 功能权限服务
- 功能权限管理和验证服务

#### impl/ - 服务实现类
- 各种服务接口的具体实现

#### license/ - 许可证服务
- 许可证查询、验证、同步服务

#### metadata/ - 元数据服务
- 元数据查询、管理、统计服务

#### organization/ - 组织架构服务
- 部门、员工管理服务

### 10. task 包 - 后台任务
- **DelAuditLogService.groovy**: 审计日志清理任务，定时清理过期日志

### 11. util 包 - 工具类
按功能分类的工具类：

#### 根目录通用工具
- `ApiNameToStoreTableNameUtil.groovy`: API名称到存储表名转换
- `DataMaskingUtil.groovy`: 数据脱敏工具
- `HttpClientUtil.groovy`: HTTP客户端工具
- `DateFormatUtil.groovy`: 日期格式化工具
- `PackUtil.groovy`: 打包工具

#### 业务模块工具
- `datarights/`: 数据权限相关工具
- `dts/`: 数据传输相关工具
- `metadata/`: 元数据处理工具
- `organization/`: 组织架构工具
- `workflow/`: 工作流工具

### 12. web 包 - 控制器层
按功能模块组织的Web控制器：

#### 根目录控制器
- `HomeController.groovy`: 首页控制器
- `BizconfController.groovy`: 业务配置控制器
- `FreemarkerConfig.groovy`: FreeMarker模板配置
- `SpecialTableController.groovy`: 专表管理控制器
- `AnonumousController.groovy`: 匿名访问控制器

#### 功能模块控制器
- `audit/`: 审计日志控制器
- `basicPlatform/`: 基础平台控制器
- `cms/`: 配置管理控制器
- `datarights/`: 数据权限控制器
- `dts/`: 数据传输控制器
- `functional/`: 功能权限控制器
- `metadata/`: 元数据控制器
- `organization/`: 组织架构控制器
- `workflow/`: 工作流控制器
- `hamster/`: 数据迁移控制器
- `hubble/`: Hubble监控控制器
- `i18n/`: 国际化控制器
- `sandBox/`: 沙箱环境控制器
- `search/`: 搜索控制器
- `transfer/`: 传输控制器

## http 包 - API测试文件

### 文件说明
- **CmsController.http**: 配置管理系统API测试
- **DataCountController.http**: 数据统计API测试
- **bizconfQuery.http**: 业务配置查询API测试
- **paas.http**: PAAS平台通用API测试

### 使用方式
这些HTTP文件是IntelliJ IDEA的HTTP Client格式，可以直接在IDE中执行：
1. 在IDE中打开.http文件
2. 点击请求旁边的运行按钮
3. 查看响应结果
4. 可以修改参数进行不同场景的测试

## 项目结构特点

1. **清晰的分层架构**: Web -> Service -> Mapper -> Entity
2. **模块化设计**: 按业务功能组织代码结构
3. **多语言支持**: Java和Groovy混合开发
4. **完整的测试支持**: 包含HTTP测试文件和单元测试
5. **灵活的配置**: 支持多环境配置和动态配置
